/* Palantir Website Replica - Components CSS */

/* Navigation Styles */
.main-navigation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 2rem;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu li a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 400;
    transition: opacity 0.3s ease;
}

.nav-menu li a:hover {
    opacity: 0.7;
}

/* Button Components */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    font-size: 1rem;
}

.btn-primary {
    background-color: #ffffff;
    color: #000000;
}

.btn-primary:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: transparent;
    color: #ffffff;
    border: 1px solid #ffffff;
}

.btn-secondary:hover {
    background-color: #ffffff;
    color: #000000;
}

.btn-ghost {
    background-color: transparent;
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-ghost:hover {
    border-color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Card Components */
.card {
    background-color: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 2rem;
    transition: all 0.3s ease;
}

.card:hover {
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-4px);
}

.card-header {
    margin-bottom: 1.5rem;
}

.card-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.card-subtitle {
    opacity: 0.7;
    font-size: 0.9rem;
}

.card-content {
    margin-bottom: 1.5rem;
}

.card-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
}

/* Grid System */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* Typography Components */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-large { font-size: 1.25rem; }
.text-small { font-size: 0.875rem; }
.text-xs { font-size: 0.75rem; }

.text-light { font-weight: 300; }
.text-normal { font-weight: 400; }
.text-medium { font-weight: 500; }
.text-semibold { font-weight: 600; }
.text-bold { font-weight: 700; }

.text-muted { opacity: 0.7; }
.text-faded { opacity: 0.5; }

/* Spacing Utilities */
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 3rem; }

.pt-1 { padding-top: 0.5rem; }
.pt-2 { padding-top: 1rem; }
.pt-3 { padding-top: 1.5rem; }
.pt-4 { padding-top: 2rem; }
.pt-5 { padding-top: 3rem; }

.pb-1 { padding-bottom: 0.5rem; }
.pb-2 { padding-bottom: 1rem; }
.pb-3 { padding-bottom: 1.5rem; }
.pb-4 { padding-bottom: 2rem; }
.pb-5 { padding-bottom: 3rem; }

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-4px);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

/* Borders */
.border-top { border-top: 1px solid rgba(255, 255, 255, 0.1); }
.border-bottom { border-bottom: 1px solid rgba(255, 255, 255, 0.1); }
.border-left { border-left: 1px solid rgba(255, 255, 255, 0.1); }
.border-right { border-right: 1px solid rgba(255, 255, 255, 0.1); }
.border-all { border: 1px solid rgba(255, 255, 255, 0.1); }

/* Background Utilities */
.bg-transparent { background-color: transparent; }
.bg-dark { background-color: rgba(0, 0, 0, 0.5); }
.bg-light { background-color: rgba(255, 255, 255, 0.02); }
.bg-accent { background-color: rgba(255, 255, 255, 0.1); }

/* Display Utilities */
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

/* Flex Utilities */
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }
.align-stretch { align-items: stretch; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

/* Position Utilities */
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

/* Responsive Utilities */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .col-sm-12 { flex: 0 0 100%; }
    .col-sm-6 { flex: 0 0 50%; }
    .col-sm-4 { flex: 0 0 33.333333%; }
    .col-sm-3 { flex: 0 0 25%; }
    
    .text-sm-center { text-align: center; }
    .text-sm-left { text-align: left; }
    
    .d-sm-none { display: none; }
    .d-sm-block { display: block; }
    .d-sm-flex { display: flex; }
}

@media (max-width: 480px) {
    .col-xs-12 { flex: 0 0 100%; }

    .text-xs-center { text-align: center; }

    .d-xs-none { display: none; }
    .d-xs-block { display: block; }
}

/* Video Overlay */
.video-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.video-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.video-container iframe {
    width: 100%;
    height: auto;
    min-height: 450px;
    border-radius: 8px;
}

.close-video {
    position: absolute;
    top: -40px;
    right: -40px;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 2rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.close-video:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Loading States */
.loaded .ptcom-design__pageContainer__ec3r6t {
    display: none !important;
}

.loaded .main-content {
    opacity: 1;
    animation: fadeIn 1s ease-out;
}

/* Additional Navigation Styles */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-actions .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-menu {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .nav-actions {
        justify-content: center;
    }

    .video-container iframe {
        min-height: 250px;
    }

    .close-video {
        top: -30px;
        right: -30px;
        font-size: 1.5rem;
        width: 30px;
        height: 30px;
    }
}
