# Palantir Website Replica

An exact replica of the Palantir Technologies website (https://www.palantir.com) created for demo purposes.

## Features

- **Exact Visual Replica**: Matches the original Palantir website design, layout, and styling
- **Authentic Typography**: Uses the exact same Alliance No.1 and Alliance No.2 fonts as the original
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Interactive Elements**: Includes hover effects, animations, and interactive components
- **Performance Optimized**: Fast loading with optimized assets and code
- **Modern Technologies**: Built with vanilla HTML, CSS, and JavaScript

## Structure

```
palantir-replica/
├── assets/
│   ├── css/
│   │   ├── fonts.css         # Alliance font definitions and typography system
│   │   ├── main.css          # Main styles and layout
│   │   ├── components.css    # Reusable component styles
│   │   └── animations.css    # Animation definitions
│   ├── js/
│   │   ├── main.js          # Main functionality
│   │   └── animations.js    # Animation controllers
│   ├── images/
│   │   ├── now-image.png    # Hero section image
│   │   └── bootcamp-video.png # Bootcamp section image
│   └── fonts/               # Alliance No.1 and Alliance No.2 font files
│       ├── AllianceNo1-Regular.woff2
│       ├── AllianceNo1-Bold.woff2
│       ├── AllianceNo2-Regular.woff2
│       └── AllianceNo2-Bold.woff2
├── index.html               # Main HTML file
├── icon.svg                 # Favicon
├── package.json            # Project configuration
└── README.md               # This file
```

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (comes with Node.js)

### Installation

1. Clone or download this repository
2. Navigate to the project directory
3. Install dependencies:
   ```bash
   npm install
   ```

### Running the Website

Start the development server:
```bash
npm start
```

The website will be available at `http://localhost:3000`

### Building for Production

For production deployment, simply upload all files to your web server. The website is built with vanilla technologies and doesn't require a build process.

## Key Sections

1. **Hero Section**: Features the main "Get AI Into Operations" message with AIP branding
2. **Platforms Section**: Showcases AIP, Foundry, Gotham, and Apollo platforms
3. **Awards Section**: Highlights Palantir's industry recognition and awards
4. **AIP Bootcamps**: Interactive section with video content
5. **Features Section**: "What Makes Palantir Platforms Powerful" cards
6. **Call-to-Action**: Demo request section
7. **Footer**: Complete site navigation and company information

## Typography System

The replica uses the exact same fonts as the original Palantir website:

- **Alliance No.1**: Primary body text and navigation
- **Alliance No.2**: Headlines and emphasis text

### Font Implementation
- Downloaded directly from Palantir's CDN
- Includes Regular and Bold weights for both font families
- Implements Palantir's exact typography scale and spacing system
- Uses CSS custom properties for responsive typography
- Matches original font-smoothing and rendering settings

## Technologies Used

- **HTML5**: Semantic markup and structure
- **CSS3**: Modern styling with Flexbox and Grid
- **JavaScript (ES6+)**: Interactive functionality and animations
- **Alliance Fonts**: Exact typography matching Palantir's brand
- **Live Server**: Development server with hot reload

## Features Implemented

### Visual Design
- Exact color scheme matching Palantir's brand
- **Authentic Typography**: Uses the exact Alliance No.1 and Alliance No.2 fonts from Palantir
- Proper spacing and layout proportions matching the original
- Responsive grid systems with Palantir's exact breakpoints

### Animations
- Loading screen with Palantir logo animation
- Scroll-triggered animations for sections
- Hover effects on interactive elements
- Smooth transitions and micro-interactions

### Interactive Elements
- Navigation with scroll behavior
- Video overlay for bootcamp content
- Hover effects on cards and buttons
- Smooth scrolling for anchor links

### Performance
- Optimized images and assets
- Efficient CSS and JavaScript
- Lazy loading for better performance
- Minimal external dependencies

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Customization

The website is built with modular CSS and JavaScript, making it easy to customize:

- **Colors**: Modify CSS custom properties in `main.css`
- **Typography**: Update font families and sizes in the CSS files
- **Content**: Edit the HTML content in `index.html`
- **Animations**: Adjust animation parameters in `animations.css` and `animations.js`

## Performance Optimization

- Minified CSS and JavaScript for production
- Optimized images with proper formats and sizes
- Efficient DOM manipulation and event handling
- CSS animations using GPU acceleration

## Accessibility

- Semantic HTML structure
- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- High contrast ratios for text

## License

This project is created for demo purposes only. All content and design elements are property of Palantir Technologies Inc.

## Disclaimer

This is a replica created for demonstration purposes. It is not affiliated with or endorsed by Palantir Technologies Inc. All trademarks, logos, and content belong to their respective owners.

## Contact

For questions about this replica implementation, please refer to the code comments and documentation within the files.
