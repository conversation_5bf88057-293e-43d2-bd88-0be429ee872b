/* Palantir Alliance Fonts - Exact Match */

/* Alliance No.1 - Regular */
@font-face {
    font-display: swap;
    font-family: "Alliance No.1";
    src: url("../fonts/AllianceNo1-Regular.woff2") format("woff2");
    font-weight: 400;
    font-style: normal;
}

/* Alliance No.1 - Bold */
@font-face {
    font-display: swap;
    font-family: "Alliance No.1";
    src: url("../fonts/AllianceNo1-Bold.woff2") format("woff2");
    font-weight: 700;
    font-style: normal;
}

/* Alliance No.2 - Regular */
@font-face {
    font-display: swap;
    font-family: "Alliance No.2";
    src: url("../fonts/AllianceNo2-Regular.woff2") format("woff2");
    font-weight: 400;
    font-style: normal;
}

/* Alliance No.2 - Bold */
@font-face {
    font-display: swap;
    font-family: "Alliance No.2";
    src: url("../fonts/AllianceNo2-Bold.woff2") format("woff2");
    font-weight: 700;
    font-style: normal;
}

/* Font Variables - Matching Palantir's Exact Typography System */
:root {
    /* Headline Typography */
    --headline-100-size: 1.8888888889rem;
    --headline-100-line: 1;
    --headline-100-letter: normal;
    --headline-100-family: "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    
    --headline-200-size: 1.8888888889rem;
    --headline-200-line: 1.1765;
    --headline-200-letter: normal;
    --headline-200-family: "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    
    --headline-300-size: 1.8888888889rem;
    --headline-300-line: 1.1765;
    --headline-300-letter: normal;
    --headline-300-family: "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    
    /* Body Typography */
    --body-100-size: 0.8888888889rem;
    --body-100-line: 1.4286;
    --body-200-size: 1rem;
    --body-200-line: 1.3889;
    --body-300-size: 1rem;
    --body-300-line: 1.3;
    --body-400-size: 1.1111111111rem;
    --body-400-line: 1.3;
    
    /* Details Typography */
    --details-captions-size: 0.5555555556rem;
    --details-captions-line: 1.6;
    --details-captions-letter: 0.05em;
    --details-captions-family: "Alliance No.2", "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    
    --details-earmark-family: "Alliance No.2", "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    
    /* Form Typography */
    --form-placeholder-size: 0.7777777778rem;
}

/* Responsive Typography Adjustments */
@media (min-width: 35em) {
    :root {
        --headline-100-size: 2.2222222222rem;
        --headline-100-line: 1;
        --headline-100-letter: -0.02em;
        --headline-100-family: "Alliance No.2", "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
        
        --headline-300-size: 2.7777777778rem;
        --headline-300-line: 1.2;
        --headline-300-letter: -0.02em;
        --headline-300-family: "Alliance No.2", "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
        
        --body-300-size: 1.1111111111rem;
        --body-300-line: 1.3;
    }
}

@media (min-width: 60em) {
    :root {
        --headline-100-size: 2.7777777778rem;
        
        --headline-200-size: 4rem;
        --headline-200-line: 1.1944;
        --headline-200-letter: -0.02em;
        --headline-200-family: "Alliance No.2", "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
        
        --headline-300-size: 5.5555555556rem;
        --headline-300-line: 1.15;
        --headline-300-letter: -0.02em;
        --headline-300-family: "Alliance No.2", "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
        
        --body-400-size: 1.8888888889rem;
        --body-400-line: 1.1765;
    }
}

/* Base Font Size */
html {
    font-size: 1.125em; /* 18px base font size matching Palantir */
}

/* Typography Classes */
.headline-100 {
    font-family: var(--headline-100-family);
    font-size: var(--headline-100-size);
    line-height: var(--headline-100-line);
    letter-spacing: var(--headline-100-letter);
}

.headline-200 {
    font-family: var(--headline-200-family);
    font-size: var(--headline-200-size);
    line-height: var(--headline-200-line);
    letter-spacing: var(--headline-200-letter);
}

.headline-300 {
    font-family: var(--headline-300-family);
    font-size: var(--headline-300-size);
    line-height: var(--headline-300-line);
    letter-spacing: var(--headline-300-letter);
}

.body-100 {
    font-size: var(--body-100-size);
    line-height: var(--body-100-line);
}

.body-200 {
    font-size: var(--body-200-size);
    line-height: var(--body-200-line);
}

.body-300 {
    font-size: var(--body-300-size);
    line-height: var(--body-300-line);
}

.body-400 {
    font-size: var(--body-400-size);
    line-height: var(--body-400-line);
}

.details-captions {
    font-family: var(--details-captions-family);
    font-size: var(--details-captions-size);
    line-height: var(--details-captions-line);
    letter-spacing: var(--details-captions-letter);
}

.details-earmark {
    font-family: var(--details-earmark-family);
}
