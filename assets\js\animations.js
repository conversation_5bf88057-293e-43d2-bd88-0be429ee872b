// Palantir Website Replica - Animations JavaScript

// Animation Controller
class AnimationController {
    constructor() {
        this.observers = new Map();
        this.animationQueue = [];
        this.isAnimating = false;
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupHoverAnimations();
        this.setupLoadingAnimations();
    }

    // Intersection Observer for scroll-triggered animations
    setupIntersectionObserver() {
        const options = {
            threshold: [0.1, 0.3, 0.5, 0.7, 0.9],
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerAnimation(entry.target);
                }
            });
        }, options);

        // Observe elements with animation classes
        const animatedElements = document.querySelectorAll('[class*="animate-"], .scroll-animate');
        animatedElements.forEach(el => observer.observe(el));

        this.observers.set('scroll', observer);
    }

    // Trigger animation based on element type
    triggerAnimation(element) {
        const animationType = this.getAnimationType(element);
        
        switch (animationType) {
            case 'fade-in':
                this.fadeIn(element);
                break;
            case 'slide-up':
                this.slideUp(element);
                break;
            case 'scale-in':
                this.scaleIn(element);
                break;
            case 'stagger':
                this.staggerChildren(element);
                break;
            default:
                this.defaultAnimation(element);
        }
    }

    // Determine animation type from element classes
    getAnimationType(element) {
        const classes = element.className;
        
        if (classes.includes('fade-in')) return 'fade-in';
        if (classes.includes('slide-up')) return 'slide-up';
        if (classes.includes('scale-in')) return 'scale-in';
        if (classes.includes('stagger')) return 'stagger';
        
        return 'default';
    }

    // Animation methods
    fadeIn(element, duration = 600) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = `opacity ${duration}ms ease, transform ${duration}ms ease`;
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        });
    }

    slideUp(element, duration = 600) {
        element.style.transform = 'translateY(100%)';
        element.style.transition = `transform ${duration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`;
        
        requestAnimationFrame(() => {
            element.style.transform = 'translateY(0)';
        });
    }

    scaleIn(element, duration = 600) {
        element.style.opacity = '0';
        element.style.transform = 'scale(0.8)';
        element.style.transition = `opacity ${duration}ms ease, transform ${duration}ms cubic-bezier(0.34, 1.56, 0.64, 1)`;
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
            element.style.transform = 'scale(1)';
        });
    }

    staggerChildren(element, delay = 100) {
        const children = Array.from(element.children);
        
        children.forEach((child, index) => {
            child.style.opacity = '0';
            child.style.transform = 'translateY(20px)';
            child.style.transition = 'opacity 600ms ease, transform 600ms ease';
            
            setTimeout(() => {
                child.style.opacity = '1';
                child.style.transform = 'translateY(0)';
            }, index * delay);
        });
    }

    defaultAnimation(element) {
        this.fadeIn(element);
    }

    // Scroll-based animations
    setupScrollAnimations() {
        let ticking = false;
        
        const updateAnimations = () => {
            const scrollY = window.pageYOffset;
            const windowHeight = window.innerHeight;
            
            // Parallax effects
            this.updateParallax(scrollY);
            
            // Progress indicators
            this.updateProgress(scrollY);
            
            ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateAnimations);
                ticking = true;
            }
        });
    }

    updateParallax(scrollY) {
        const parallaxElements = document.querySelectorAll('.parallax, .hero-image img');
        
        parallaxElements.forEach(element => {
            const speed = element.dataset.speed || 0.5;
            const yPos = -(scrollY * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    }

    updateProgress(scrollY) {
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const progress = (scrollY / docHeight) * 100;
        
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            bar.style.width = `${progress}%`;
        });
    }

    // Hover animations
    setupHoverAnimations() {
        const hoverElements = document.querySelectorAll('.hover-animate, .platform-item, .feature-card');
        
        hoverElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                this.onHoverEnter(element);
            });
            
            element.addEventListener('mouseleave', () => {
                this.onHoverLeave(element);
            });
        });
    }

    onHoverEnter(element) {
        element.style.transform = 'translateY(-8px) scale(1.02)';
        element.style.boxShadow = '0 20px 40px rgba(255, 255, 255, 0.1)';
        element.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    }

    onHoverLeave(element) {
        element.style.transform = 'translateY(0) scale(1)';
        element.style.boxShadow = 'none';
    }

    // Loading animations
    setupLoadingAnimations() {
        const logo = document.querySelector('.ptcom-design__palantirLogo__ec3r6t');
        
        if (logo) {
            this.animateLogo(logo);
        }
    }

    animateLogo(logo) {
        const svg = logo.querySelector('svg');
        const path = svg.querySelector('path');
        
        // Animate SVG path
        if (path) {
            const pathLength = path.getTotalLength();
            path.style.strokeDasharray = pathLength;
            path.style.strokeDashoffset = pathLength;
            path.style.stroke = '#ffffff';
            path.style.strokeWidth = '2';
            path.style.fill = 'none';
            
            // Animate path drawing
            path.style.animation = 'drawPath 2s ease-in-out forwards';
            
            // Add CSS for path animation
            if (!document.querySelector('#path-animation-styles')) {
                const style = document.createElement('style');
                style.id = 'path-animation-styles';
                style.textContent = `
                    @keyframes drawPath {
                        to {
                            stroke-dashoffset: 0;
                        }
                    }
                `;
                document.head.appendChild(style);
            }
            
            // Fill animation after path is drawn
            setTimeout(() => {
                path.style.fill = '#ffffff';
                path.style.stroke = 'none';
            }, 2000);
        }
    }

    // Utility methods
    addAnimation(element, animationName, duration = 600, delay = 0) {
        return new Promise((resolve) => {
            setTimeout(() => {
                element.style.animation = `${animationName} ${duration}ms ease-out forwards`;
                
                element.addEventListener('animationend', () => {
                    resolve();
                }, { once: true });
            }, delay);
        });
    }

    removeAnimation(element) {
        element.style.animation = '';
        element.style.transition = '';
    }

    // Chain animations
    async chainAnimations(animations) {
        for (const animation of animations) {
            await this.addAnimation(
                animation.element,
                animation.name,
                animation.duration,
                animation.delay
            );
        }
    }

    // Performance optimization
    optimizeAnimations() {
        // Use will-change for elements that will be animated
        const animatedElements = document.querySelectorAll('[class*="animate-"]');
        animatedElements.forEach(el => {
            el.style.willChange = 'transform, opacity';
        });

        // Remove will-change after animation
        setTimeout(() => {
            animatedElements.forEach(el => {
                el.style.willChange = 'auto';
            });
        }, 5000);
    }

    // Cleanup
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        this.animationQueue = [];
    }
}

// Text animation effects
class TextAnimations {
    static typeWriter(element, text, speed = 50) {
        element.textContent = '';
        let i = 0;
        
        const timer = setInterval(() => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
            } else {
                clearInterval(timer);
            }
        }, speed);
    }

    static fadeInWords(element, delay = 100) {
        const words = element.textContent.split(' ');
        element.innerHTML = words.map(word => `<span style="opacity: 0;">${word}</span>`).join(' ');
        
        const spans = element.querySelectorAll('span');
        spans.forEach((span, index) => {
            setTimeout(() => {
                span.style.transition = 'opacity 0.6s ease';
                span.style.opacity = '1';
            }, index * delay);
        });
    }

    static countUp(element, target, duration = 2000) {
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                element.textContent = target;
                clearInterval(timer);
            } else {
                element.textContent = Math.floor(current);
            }
        }, 16);
    }
}

// Initialize animations when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const animationController = new AnimationController();
    
    // Optimize animations for performance
    animationController.optimizeAnimations();
    
    // Add text animations to specific elements
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        TextAnimations.fadeInWords(heroTitle, 150);
    }
    
    // Make animation controller globally available
    window.animationController = animationController;
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AnimationController, TextAnimations };
}
