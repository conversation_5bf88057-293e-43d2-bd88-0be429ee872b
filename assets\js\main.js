// Palantir Website Replica - Main JavaScript

// DOM Ready
document.addEventListener('DOMContentLoaded', function() {
    initializeWebsite();
});

// Initialize all website functionality
function initializeWebsite() {
    initializeLoading();
    initializeScrollAnimations();
    initializeNavigation();
    initializeParallax();
    initializeHoverEffects();
    initializeVideoHandling();
    initializeFormHandling();
    initializeAnalytics();
}

// Loading Animation
function initializeLoading() {
    const logo = document.querySelector('.ptcom-design__palantirLogo__ec3r6t');
    const pageContainer = document.querySelector('.ptcom-design__pageContainer__ec3r6t');
    
    if (logo) {
        // Show loading animation
        setTimeout(() => {
            logo.style.opacity = '0';
            pageContainer.style.opacity = '0';
            
            setTimeout(() => {
                pageContainer.style.display = 'none';
                document.body.classList.add('loaded');
            }, 1000);
        }, 3000);
    }
}

// Scroll Animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe all sections
    const sections = document.querySelectorAll('section');
    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        observer.observe(section);
    });

    // Stagger animation for grid items
    const gridItems = document.querySelectorAll('.platforms-grid .platform-item, .features-grid .feature-card, .awards-grid .award-item');
    gridItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.animationDelay = `${index * 0.1}s`;
        observer.observe(item);
    });
}

// Navigation
function initializeNavigation() {
    const header = document.querySelector('.main-header');
    const hamburger = document.querySelector('.hamburger');
    const slideNav = document.getElementById('slideNav');
    const navOverlay = document.getElementById('navOverlay');

    // Header scroll behavior
    if (header) {
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;

            if (currentScrollY > 100) {
                header.style.backgroundColor = 'rgba(0, 0, 0, 0.95)';
                header.style.backdropFilter = 'blur(20px)';
            } else {
                header.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
                header.style.backdropFilter = 'blur(10px)';
            }

            lastScrollY = currentScrollY;
        });
    }

    // Hamburger menu functionality
    if (hamburger && slideNav && navOverlay) {
        // Toggle navigation
        function toggleNav() {
            const isOpen = slideNav.classList.contains('is-open');

            if (isOpen) {
                closeNav();
            } else {
                openNav();
            }
        }

        function openNav() {
            hamburger.classList.add('is-active');
            slideNav.classList.add('is-open');
            navOverlay.classList.add('is-open');
            document.body.style.overflow = 'hidden';
        }

        function closeNav() {
            hamburger.classList.remove('is-active');
            slideNav.classList.remove('is-open');
            navOverlay.classList.remove('is-open');
            document.body.style.overflow = '';
        }

        // Event listeners
        hamburger.addEventListener('click', toggleNav);
        navOverlay.addEventListener('click', closeNav);

        // Close nav on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && slideNav.classList.contains('is-open')) {
                closeNav();
            }
        });

        // Close nav when clicking on navigation links
        const navLinks = slideNav.querySelectorAll('a');
        navLinks.forEach(link => {
            link.addEventListener('click', closeNav);
        });
    }
}

// Parallax Effects
function initializeParallax() {
    const parallaxElements = document.querySelectorAll('.hero-image, .bootcamp-video');
    
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        
        parallaxElements.forEach(element => {
            element.style.transform = `translateY(${rate}px)`;
        });
    });
}

// Hover Effects
function initializeHoverEffects() {
    // Add hover effects to cards
    const cards = document.querySelectorAll('.platform-item, .feature-card, .award-item');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
            this.style.boxShadow = '0 20px 40px rgba(255, 255, 255, 0.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });

    // Button hover effects
    const buttons = document.querySelectorAll('.btn, .hero-cta, .bootcamp-cta, .main-cta');
    
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// Video Handling
function initializeVideoHandling() {
    const videoContainers = document.querySelectorAll('.bootcamp-video');
    
    videoContainers.forEach(container => {
        const img = container.querySelector('img');
        if (img) {
            img.addEventListener('click', function() {
                // Create video overlay
                const overlay = document.createElement('div');
                overlay.className = 'video-overlay';
                overlay.innerHTML = `
                    <div class="video-container">
                        <iframe src="https://player.vimeo.com/video/877938815?autoplay=1" 
                                width="800" height="450" frameborder="0" 
                                allow="autoplay; fullscreen; picture-in-picture" 
                                allowfullscreen></iframe>
                        <button class="close-video">&times;</button>
                    </div>
                `;
                
                document.body.appendChild(overlay);
                
                // Close video
                overlay.querySelector('.close-video').addEventListener('click', () => {
                    document.body.removeChild(overlay);
                });
                
                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        document.body.removeChild(overlay);
                    }
                });
            });
        }
    });
}

// Form Handling
function initializeFormHandling() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Add loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.textContent = 'Submitting...';
                submitBtn.disabled = true;
            }
            
            // Simulate form submission
            setTimeout(() => {
                alert('Thank you for your interest! We will contact you soon.');
                this.reset();
                
                if (submitBtn) {
                    submitBtn.textContent = 'Submit';
                    submitBtn.disabled = false;
                }
            }, 2000);
        });
    });
}

// Analytics (Google Tag Manager)
function initializeAnalytics() {
    // Track button clicks
    const trackableElements = document.querySelectorAll('a[href*="aip"], a[href*="foundry"], a[href*="gotham"], a[href*="apollo"]');
    
    trackableElements.forEach(element => {
        element.addEventListener('click', function() {
            if (typeof gtag !== 'undefined') {
                gtag('event', 'click', {
                    event_category: 'Platform Link',
                    event_label: this.textContent.trim(),
                    value: 1
                });
            }
        });
    });

    // Track scroll depth
    let maxScroll = 0;
    const milestones = [25, 50, 75, 100];
    
    window.addEventListener('scroll', () => {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        
        if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent;
            
            milestones.forEach(milestone => {
                if (scrollPercent >= milestone && maxScroll < milestone + 5) {
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'scroll', {
                            event_category: 'Scroll Depth',
                            event_label: `${milestone}%`,
                            value: milestone
                        });
                    }
                }
            });
        }
    });
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Performance monitoring
if ('performance' in window) {
    window.addEventListener('load', () => {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('Page Load Time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
        }, 0);
    });
}

// Error handling
window.addEventListener('error', (e) => {
    console.error('JavaScript Error:', e.error);
    
    if (typeof gtag !== 'undefined') {
        gtag('event', 'exception', {
            description: e.error.toString(),
            fatal: false
        });
    }
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializeWebsite,
        debounce,
        throttle
    };
}
