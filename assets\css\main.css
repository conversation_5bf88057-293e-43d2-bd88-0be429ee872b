/* Palantir Website Replica - Main CSS */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    background-color: #000000;
    color: #ffffff;
    overflow-x: hidden;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    line-height: 1.**********;
}

/* Main content wrapper */
.main-content {
    padding-top: 80px; /* Account for fixed header */
}

/* Page Container */
.ptcom-design__pageContainer__ec3r6t {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.ptcom-design__pageShape__ec3r6t {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
}

.ptcom-design__pageShapeGray__ec3r6t {
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 50%, #000000 100%);
}

/* Loading Logo */
.ptcom-design__palantirLogo__ec3r6t {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    opacity: 0;
    animation: logoFadeIn 2s ease-in-out forwards;
}

.ptcom-design__palantirLogo__ec3r6t svg {
    width: 100%;
    height: 100%;
}

@keyframes logoFadeIn {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(1); }
}

/* Main Content */
.main-content {
    position: relative;
    z-index: 1;
    padding-top: 100px;
    opacity: 0;
    transition: opacity 1s ease-out;
}

body.loaded .main-content {
    opacity: 1;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 20px;
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.02)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.hero-content {
    max-width: 1200px;
    width: 100%;
    position: relative;
    z-index: 1;
    animation: fadeInUp 1.2s ease-out 0.5s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-title {
    font-family: "Alliance No.2", "Alliance No.1", "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    font-size: var(--headline-200-size, 4rem);
    font-weight: 700; /* Use bold weight available in Alliance fonts */
    margin-bottom: 2rem;
    letter-spacing: -0.02em; /* Match the original Palantir letter spacing */
    line-height: var(--headline-200-line, 1.1944);
    color: #ffffff;
    text-align: center;
    text-transform: none;
}

.hero-image {
    margin: 3rem 0;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
}

.hero-cta {
    display: inline-block;
    padding: 12px 24px;
    background-color: transparent;
    border: 1px solid #ffffff;
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.hero-cta:hover {
    background-color: #ffffff;
    color: #000000;
}

/* Section Styles */
section {
    padding: 80px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    font-family: "Alliance No.2", "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: var(--headline-100-size, 2.5rem);
    font-weight: 400;
    margin-bottom: 1rem;
    text-align: center;
    letter-spacing: var(--headline-100-letter, -0.02em);
    line-height: var(--headline-100-line, 1);
}

.section-subtitle {
    font-size: 1.2rem;
    text-align: center;
    margin-bottom: 3rem;
    opacity: 0.8;
}

/* Platforms Section */
.platforms-section {
    background-color: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    margin: 40px auto;
}

.platforms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.platform-item {
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.platform-item:hover {
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-4px);
}

.platform-item h3 {
    font-family: "Alliance No.2", "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 1rem;
    color: #ffffff;
}

.platform-item p {
    margin-bottom: 1.5rem;
    opacity: 0.8;
}

.platform-item a {
    color: #ffffff;
    text-decoration: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    transition: border-color 0.3s ease;
}

.platform-item a:hover {
    border-color: #ffffff;
}

/* Awards Section */
.awards-section {
    padding: 100px 20px;
}

.awards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.award-item {
    padding: 2rem;
    background-color: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
}

.award-item h3 {
    font-family: "Alliance No.2", "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.award-item p {
    margin-bottom: 1rem;
    opacity: 0.8;
    line-height: 1.6;
}

.award-item a {
    color: #ffffff;
    text-decoration: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    transition: border-color 0.3s ease;
}

.award-item a:hover {
    border-color: #ffffff;
}

/* Bootcamps Section */
.bootcamps-section {
    padding: 100px 20px;
    background-color: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    margin: 40px auto;
}

.bootcamp-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.bootcamp-text h2 {
    font-family: "Alliance No.2", "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: var(--headline-100-size, 2.5rem);
    margin-bottom: 1.5rem;
    font-weight: 400;
    letter-spacing: var(--headline-100-letter, -0.02em);
    line-height: var(--headline-100-line, 1);
}

.bootcamp-text p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
    line-height: 1.6;
}

.bootcamp-cta {
    display: inline-block;
    padding: 12px 24px;
    background-color: #ffffff;
    color: #000000;
    text-decoration: none;
    font-weight: 500;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.bootcamp-cta:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.bootcamp-video img {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

/* Features Section */
.features-section {
    padding: 100px 20px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.feature-card:hover {
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-4px);
}

.feature-card h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    opacity: 0.6;
}

.feature-card h4 {
    font-family: "Alliance No.2", "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.feature-card p {
    margin-bottom: 1rem;
    opacity: 0.8;
    line-height: 1.6;
}

.feature-card a {
    color: #ffffff;
    text-decoration: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    transition: border-color 0.3s ease;
}

.feature-card a:hover {
    border-color: #ffffff;
}

/* CTA Section */
.cta-section {
    text-align: center;
    padding: 100px 20px;
    background-color: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    margin: 40px auto;
}

.cta-section h2 {
    font-family: "Alliance No.2", "Alliance No.1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: var(--headline-100-size, 2.5rem);
    margin-bottom: 1rem;
    font-weight: 400;
    letter-spacing: var(--headline-100-letter, -0.02em);
    line-height: var(--headline-100-line, 1);
}

.cta-section p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
}

.main-cta {
    display: inline-block;
    padding: 16px 32px;
    background-color: #ffffff;
    color: #000000;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.main-cta:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

/* Footer */
.main-footer {
    background-color: rgba(255, 255, 255, 0.02);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 60px 20px 20px;
    margin-top: 100px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h4 {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #ffffff;
}

.footer-bottom {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    opacity: 0.6;
    font-size: 0.9rem;
}

.social-links {
    display: flex;
    gap: 1.5rem;
}

.social-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .bootcamp-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .platforms-grid,
    .features-grid,
    .awards-grid {
        grid-template-columns: 1fr;
    }

    section {
        padding: 60px 20px;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

/* Header Styles */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-container {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 1rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.header-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.header-logo svg {
    width: 32px;
    height: 32px;
}

/* Full-width search bar */
.header-search {
    position: relative;
    flex: 1;
    max-width: none;
}

.header-search input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 10px 40px 10px 16px;
    color: white;
    font-size: 14px;
    width: 100%;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.header-search input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.4);
}

.header-search input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.header-search .search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.6);
    pointer-events: none;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
}

.get-started-btn {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.get-started-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Hamburger Menu */
.hamburger {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.hamburger-box {
    width: 24px;
    height: 18px;
    display: inline-block;
    position: relative;
}

.hamburger-inner {
    display: block;
    top: 50%;
    margin-top: -1px;
}

.hamburger-inner,
.hamburger-inner::before,
.hamburger-inner::after {
    width: 24px;
    height: 2px;
    background-color: white;
    border-radius: 2px;
    position: absolute;
    transition-property: transform;
    transition-duration: 0.15s;
    transition-timing-function: ease;
}

.hamburger-inner::before,
.hamburger-inner::after {
    content: "";
    display: block;
}

.hamburger-inner::before {
    top: -6px;
}

.hamburger-inner::after {
    bottom: -6px;
}

/* Slide-out Navigation */
.slide-nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100vh;
    background: #1a1a1a;
    z-index: 1001;
    transition: right 0.3s ease;
    overflow-y: auto;
}

.slide-nav.active {
    right: 0;
}

/* Navigation Header */
.slide-nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.2rem;
}

.nav-logo svg {
    width: 24px;
    height: 24px;
}

.nav-header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-get-started {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-get-started:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.nav-search-btn {
    background: transparent;
    border: none;
    color: white;
    padding: 8px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.nav-search-btn:hover {
    color: rgba(255, 255, 255, 0.7);
}

/* Navigation Content Grid */
.slide-nav-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 3rem;
    padding: 3rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.nav-column {
    display: flex;
    flex-direction: column;
}

/* Section Headers */
.nav-section-title {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 0.5rem;
}

.nav-section-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 1.5rem;
}

.section-link {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.7rem;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: color 0.3s ease;
}

.section-link:hover {
    color: white;
}

/* Navigation Main Menu */
.nav-main-title {
    color: white;
    font-size: 1.8rem;
    font-weight: 400;
    margin: 0 0 1rem 0;
    line-height: 1.2;
}

.nav-platforms {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.nav-platforms li {
    margin-bottom: 0.5rem;
}

.nav-platforms a {
    color: white;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 400;
    transition: color 0.3s ease;
    display: block;
    padding: 0.25rem 0;
}

.nav-platforms a:hover {
    color: rgba(255, 255, 255, 0.7);
}

.nav-main-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-main-links li {
    margin-bottom: 0.8rem;
}

.nav-main-links a {
    color: white;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 400;
    transition: color 0.3s ease;
    display: block;
    padding: 0.25rem 0;
}

.nav-main-links a:hover {
    color: rgba(255, 255, 255, 0.7);
}

/* News Items */
.news-item {
    margin-bottom: 2.5rem;
}

.news-date {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    display: block;
    margin-bottom: 0.8rem;
}

.news-content {
    display: flex;
    gap: 1rem;
}

.news-image {
    width: 80px;
    height: 60px;
    border-radius: 4px;
    flex-shrink: 0;
    background: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.news-placeholder-1 {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
}

.news-placeholder-2 {
    background: linear-gradient(135deg, #059669, #047857);
}

.news-image::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.news-text h4 {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    line-height: 1.3;
}

.news-text p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
    line-height: 1.4;
    margin: 0 0 0.5rem 0;
}

.news-link {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
    text-decoration: none;
    transition: color 0.3s ease;
}

.news-link:hover {
    color: white;
}

/* Impact Study */
.impact-study {
    margin-bottom: 2rem;
}

.impact-label {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    display: block;
    margin-bottom: 1rem;
}

.impact-image {
    margin-bottom: 1rem;
    width: 100%;
    height: 200px;
    border-radius: 4px;
    background: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.impact-placeholder {
    background: linear-gradient(135deg, #7c3aed, #5b21b6);
}

.impact-image::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

.impact-title {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    line-height: 1.3;
}

/* Offerings Content */
.offerings-content {
    margin-bottom: 2rem;
}

.offerings-content p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0 0 1rem 0;
}

.offerings-link {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    text-decoration: none;
    transition: color 0.3s ease;
}

.offerings-link:hover {
    color: white;
}

/* Quick Links */
.quick-links-title {
    margin-top: 2rem;
}

.quick-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.quick-links li {
    margin-bottom: 0.6rem;
}

.quick-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 400;
    transition: color 0.3s ease;
    display: block;
    padding: 0.2rem 0;
}

.quick-links a:hover {
    color: white;
}



/* Close button for slide nav */
.slide-nav-close {
    background: transparent;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.3s ease;
}

.slide-nav-close:hover {
    color: rgba(255, 255, 255, 0.7);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .header-search {
        display: none;
    }

    .header-container {
        gap: 1rem;
    }

    .slide-nav-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 2rem 1rem;
    }

    .nav-main-title {
        font-size: 1.5rem;
    }

    .news-content {
        flex-direction: column;
        gap: 0.5rem;
    }

    .news-image {
        width: 100%;
        height: 120px;
    }
}

@media (max-width: 1200px) {
    .slide-nav-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
}
