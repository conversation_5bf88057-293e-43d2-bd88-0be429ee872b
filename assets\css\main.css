/* Palantir Website Replica - Main CSS */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
    background-color: #000000;
    color: #ffffff;
    overflow-x: hidden;
}

/* Page Container */
.ptcom-design__pageContainer__ec3r6t {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.ptcom-design__pageShape__ec3r6t {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
}

.ptcom-design__pageShapeGray__ec3r6t {
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 50%, #000000 100%);
}

/* Loading Logo */
.ptcom-design__palantirLogo__ec3r6t {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    opacity: 0;
    animation: logoFadeIn 2s ease-in-out forwards;
}

.ptcom-design__palantirLogo__ec3r6t svg {
    width: 100%;
    height: 100%;
}

@keyframes logoFadeIn {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(1); }
}

/* Main Content */
.main-content {
    position: relative;
    z-index: 1;
    padding-top: 100px;
    opacity: 0;
    transition: opacity 1s ease-out;
}

body.loaded .main-content {
    opacity: 1;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 20px;
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.02)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.hero-content {
    max-width: 1200px;
    width: 100%;
    position: relative;
    z-index: 1;
    animation: fadeInUp 1.2s ease-out 0.5s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-title {
    font-size: 4rem;
    font-weight: 300;
    margin-bottom: 2rem;
    letter-spacing: -0.02em;
    line-height: 1.1;
    background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-image {
    margin: 3rem 0;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
}

.hero-cta {
    display: inline-block;
    padding: 12px 24px;
    background-color: transparent;
    border: 1px solid #ffffff;
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.hero-cta:hover {
    background-color: #ffffff;
    color: #000000;
}

/* Section Styles */
section {
    padding: 80px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.2rem;
    text-align: center;
    margin-bottom: 3rem;
    opacity: 0.8;
}

/* Platforms Section */
.platforms-section {
    background-color: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    margin: 40px auto;
}

.platforms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.platform-item {
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.platform-item:hover {
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-4px);
}

.platform-item h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

.platform-item p {
    margin-bottom: 1.5rem;
    opacity: 0.8;
}

.platform-item a {
    color: #ffffff;
    text-decoration: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    transition: border-color 0.3s ease;
}

.platform-item a:hover {
    border-color: #ffffff;
}

/* Awards Section */
.awards-section {
    padding: 100px 20px;
}

.awards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.award-item {
    padding: 2rem;
    background-color: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
}

.award-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.award-item p {
    margin-bottom: 1rem;
    opacity: 0.8;
    line-height: 1.6;
}

.award-item a {
    color: #ffffff;
    text-decoration: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    transition: border-color 0.3s ease;
}

.award-item a:hover {
    border-color: #ffffff;
}

/* Bootcamps Section */
.bootcamps-section {
    padding: 100px 20px;
    background-color: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    margin: 40px auto;
}

.bootcamp-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.bootcamp-text h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    font-weight: 300;
}

.bootcamp-text p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
    line-height: 1.6;
}

.bootcamp-cta {
    display: inline-block;
    padding: 12px 24px;
    background-color: #ffffff;
    color: #000000;
    text-decoration: none;
    font-weight: 500;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.bootcamp-cta:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.bootcamp-video img {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

/* Features Section */
.features-section {
    padding: 100px 20px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.feature-card:hover {
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-4px);
}

.feature-card h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    opacity: 0.6;
}

.feature-card h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.feature-card p {
    margin-bottom: 1rem;
    opacity: 0.8;
    line-height: 1.6;
}

.feature-card a {
    color: #ffffff;
    text-decoration: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    transition: border-color 0.3s ease;
}

.feature-card a:hover {
    border-color: #ffffff;
}

/* CTA Section */
.cta-section {
    text-align: center;
    padding: 100px 20px;
    background-color: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    margin: 40px auto;
}

.cta-section h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 300;
}

.cta-section p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
}

.main-cta {
    display: inline-block;
    padding: 16px 32px;
    background-color: #ffffff;
    color: #000000;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.main-cta:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

/* Footer */
.main-footer {
    background-color: rgba(255, 255, 255, 0.02);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 60px 20px 20px;
    margin-top: 100px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h4 {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #ffffff;
}

.footer-bottom {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    opacity: 0.6;
    font-size: 0.9rem;
}

.social-links {
    display: flex;
    gap: 1.5rem;
}

.social-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .bootcamp-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .platforms-grid,
    .features-grid,
    .awards-grid {
        grid-template-columns: 1fr;
    }

    section {
        padding: 60px 20px;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}
